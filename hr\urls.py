from django.urls import path
from . import views

app_name = 'hr'

urlpatterns = [
    # HR Dashboard
    path('', views.HRDashboardView.as_view(), name='dashboard'),

    # Employee Management
    path('employees/', views.EmployeeListView.as_view(), name='employees'),
    path('employees/add/', views.EmployeeCreateView.as_view(), name='employee_add'),
    path('employees/<int:pk>/', views.EmployeeDetailView.as_view(), name='employee_detail'),
    path('employees/<int:pk>/edit/', views.EmployeeUpdateView.as_view(), name='employee_edit'),
    path('employees/<int:pk>/delete/', views.EmployeeDeleteView.as_view(), name='employee_delete'),

    # Department Management
    path('departments/', views.DepartmentListView.as_view(), name='departments'),
    path('departments/add/', views.DepartmentCreateView.as_view(), name='department_add'),
    path('departments/<int:pk>/edit/', views.DepartmentUpdateView.as_view(), name='department_edit'),

    # Position Management
    path('positions/', views.PositionListView.as_view(), name='positions'),
    path('positions/add/', views.PositionCreateView.as_view(), name='position_add'),
    path('positions/<int:pk>/edit/', views.PositionUpdateView.as_view(), name='position_edit'),

    # General Settings
    path('registration-files/', views.RegistrationFilesView.as_view(), name='registration_files'),
    path('affairs-settings/', views.EmployeeAffairsSettingsView.as_view(), name='affairs_settings'),
    path('work-system/', views.WorkSystemView.as_view(), name='work_system'),
    path('attendance-devices/', views.AttendanceDevicesView.as_view(), name='attendance_devices'),
    path('attendance-devices-2/', views.AttendanceDevices2View.as_view(), name='attendance_devices_2'),
    path('permission-types/', views.PermissionTypesView.as_view(), name='permission_types'),
    path('holiday-types/', views.HolidayTypesView.as_view(), name='holiday_types'),
    path('allowance-types/', views.AllowanceTypesView.as_view(), name='allowance_types'),
    path('deduction-types/', views.DeductionTypesView.as_view(), name='deduction_types'),
    path('public-holidays/', views.PublicHolidaysView.as_view(), name='public_holidays'),

    # Work System Settings
    path('deduction-rules/', views.DeductionRulesView.as_view(), name='deduction_rules'),
    path('extra-time-rules/', views.ExtraTimeRulesView.as_view(), name='extra_time_rules'),
    path('rewards-rules/', views.RewardsRulesView.as_view(), name='rewards_rules'),
    path('penalties-rules/', views.PenaltiesRulesView.as_view(), name='penalties_rules'),

    # Attendance and Presence
    path('attendance/', views.AttendanceView.as_view(), name='attendance'),
    path('attendance/manual/', views.ManualAttendanceView.as_view(), name='manual_attendance'),
    path('attendance/reports/', views.AttendanceReportsView.as_view(), name='attendance_reports'),
    path('attendance/summary/', views.AttendanceSummaryView.as_view(), name='attendance_summary'),
    path('attendance/devices-data/', views.AttendanceDevicesDataView.as_view(), name='attendance_devices_data'),

    # Permissions and Leaves
    path('permissions/', views.PermissionsView.as_view(), name='permissions'),
    path('permissions/add/', views.AddPermissionView.as_view(), name='add_permission'),
    path('permissions/approve/', views.ApprovePermissionsView.as_view(), name='approve_permissions'),
    path('permissions/reports/', views.PermissionReportsView.as_view(), name='permission_reports'),

    # Holidays and Vacations
    path('holidays/', views.HolidaysView.as_view(), name='holidays'),
    path('leaves/', views.VacationRequestsView.as_view(), name='leaves'),  # Alias for vacation requests
    path('vacation-requests/', views.VacationRequestsView.as_view(), name='vacation_requests'),
    path('vacation-balance/', views.VacationBalanceView.as_view(), name='vacation_balance'),

    # Payroll
    path('payroll/', views.PayrollView.as_view(), name='payroll'),
    path('payroll/generate/', views.GeneratePayrollView.as_view(), name='generate_payroll'),
    path('payroll/reports/', views.PayrollReportsView.as_view(), name='payroll_reports'),
    path('salary-structure/', views.SalaryStructureView.as_view(), name='salary_structure'),
    path('allowances/', views.AllowancesView.as_view(), name='allowances'),
    path('deductions/', views.DeductionsView.as_view(), name='deductions'),
    path('bonuses/', views.BonusesView.as_view(), name='bonuses'),

    # Performance and Evaluation
    path('performance/', views.PerformanceView.as_view(), name='performance'),

    # HR Reports
    path('reports/', views.HRReportsView.as_view(), name='reports'),
    path('reports/employee/', views.EmployeeReportsView.as_view(), name='employee_reports'),
    path('reports/attendance/', views.AttendanceReportsView.as_view(), name='attendance_reports'),
    path('reports/payroll/', views.PayrollReportsView.as_view(), name='payroll_reports'),
    path('reports/performance/', views.PerformanceReportsView.as_view(), name='performance_reports'),
]
