{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Academic Dashboard" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .academic-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .academic-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .subjects-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }
    .teachers-card {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 15px;
    }
    .classes-card {
        background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        color: white;
        border-radius: 15px;
    }
    .exams-card {
        background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
        color: white;
        border-radius: 15px;
    }
    .quick-action-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        cursor: pointer;
        transition: transform 0.2s;
    }
    .quick-action-card:hover {
        transform: scale(1.05);
        color: white;
        text-decoration: none;
    }
    .schedule-item {
        border-left: 3px solid #007bff;
        padding-left: 1rem;
        margin-bottom: 1rem;
    }
    .performance-chart {
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-graduation-cap text-primary me-2"></i>{% trans "Academic Management Dashboard" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage subjects, classes, schedules, grades, and academic operations" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Subject" %}
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-calendar-plus me-2"></i>{% trans "Create Schedule" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card subjects-card">
                <div class="card-body text-center">
                    <i class="fas fa-book fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ total_subjects|default:24 }}</h3>
                    <p class="mb-0">{% trans "Total Subjects" %}</p>
                    <small class="opacity-75">{% trans "Active subjects" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card teachers-card">
                <div class="card-body text-center">
                    <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ total_teachers|default:45 }}</h3>
                    <p class="mb-0">{% trans "Teachers" %}</p>
                    <small class="opacity-75">{% trans "Teaching staff" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card classes-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ total_classes|default:18 }}</h3>
                    <p class="mb-0">{% trans "Classes" %}</p>
                    <small class="opacity-75">{% trans "Active classes" %}</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card exams-card">
                <div class="card-body text-center">
                    <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ upcoming_exams|default:8 }}</h3>
                    <p class="mb-0">{% trans "Upcoming Exams" %}</p>
                    <small class="opacity-75">{% trans "This month" %}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <h5 class="mb-3">{% trans "Quick Actions" %}</h5>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:subjects' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-book fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Subjects" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:teachers' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Teachers" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:schedules' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-calendar fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Schedules" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:grades' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Grades" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:exams' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Exams" %}</h6>
                </div>
            </a>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{% url 'academics:attendance' %}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-check fa-2x mb-2"></i>
                    <h6 class="mb-0">{% trans "Attendance" %}</h6>
                </div>
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <!-- Today's Schedule -->
        <div class="col-lg-6 mb-4">
            <div class="card academic-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-day me-2"></i>{% trans "Today's Schedule" %}
                        </h5>
                        <a href="{% url 'academics:schedules' %}" class="btn btn-sm btn-outline-primary">
                            {% trans "View All" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="schedule-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{% trans "Mathematics" %} - Grade 1-A</h6>
                                <small class="text-muted">{% trans "Teacher" %}: Ahmed Mohamed</small>
                            </div>
                            <div class="text-end">
                                <strong>08:00 - 09:00</strong>
                                <br>
                                <small class="text-muted">{% trans "Room" %} 101</small>
                            </div>
                        </div>
                    </div>
                    <div class="schedule-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{% trans "Science" %} - Grade 2-B</h6>
                                <small class="text-muted">{% trans "Teacher" %}: Fatima Hassan</small>
                            </div>
                            <div class="text-end">
                                <strong>09:00 - 10:00</strong>
                                <br>
                                <small class="text-muted">{% trans "Room" %} 205</small>
                            </div>
                        </div>
                    </div>
                    <div class="schedule-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{% trans "English" %} - Grade 3-A</h6>
                                <small class="text-muted">{% trans "Teacher" %}: Nora Ahmed</small>
                            </div>
                            <div class="text-end">
                                <strong>10:30 - 11:30</strong>
                                <br>
                                <small class="text-muted">{% trans "Room" %} 302</small>
                            </div>
                        </div>
                    </div>
                    <div class="schedule-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{% trans "Arabic" %} - Grade 1-B</h6>
                                <small class="text-muted">{% trans "Teacher" %}: Abdullah Salem</small>
                            </div>
                            <div class="text-end">
                                <strong>11:30 - 12:30</strong>
                                <br>
                                <small class="text-muted">{% trans "Room" %} 103</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Academic Performance -->
        <div class="col-lg-6 mb-4">
            <div class="card academic-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Academic Performance" %}
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="performanceChart" class="performance-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Subject Overview and Upcoming Events -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card academic-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>{% trans "Subject Overview" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Subject" %}</th>
                                    <th>{% trans "Teacher" %}</th>
                                    <th>{% trans "Classes" %}</th>
                                    <th>{% trans "Students" %}</th>
                                    <th>{% trans "Avg Grade" %}</th>
                                    <th>{% trans "Status" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calculator text-primary me-2"></i>
                                            {% trans "Mathematics" %}
                                        </div>
                                    </td>
                                    <td>Ahmed Mohamed</td>
                                    <td>6</td>
                                    <td>180</td>
                                    <td>
                                        <span class="badge bg-success">85%</span>
                                    </td>
                                    <td><span class="badge bg-success">{% trans "Excellent" %}</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-flask text-info me-2"></i>
                                            {% trans "Science" %}
                                        </div>
                                    </td>
                                    <td>Fatima Hassan</td>
                                    <td>5</td>
                                    <td>150</td>
                                    <td>
                                        <span class="badge bg-primary">82%</span>
                                    </td>
                                    <td><span class="badge bg-primary">{% trans "Good" %}</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-globe text-success me-2"></i>
                                            {% trans "English" %}
                                        </div>
                                    </td>
                                    <td>Nora Ahmed</td>
                                    <td>6</td>
                                    <td>180</td>
                                    <td>
                                        <span class="badge bg-warning">78%</span>
                                    </td>
                                    <td><span class="badge bg-warning">{% trans "Average" %}</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-book text-warning me-2"></i>
                                            {% trans "Arabic" %}
                                        </div>
                                    </td>
                                    <td>Abdullah Salem</td>
                                    <td>6</td>
                                    <td>180</td>
                                    <td>
                                        <span class="badge bg-success">88%</span>
                                    </td>
                                    <td><span class="badge bg-success">{% trans "Excellent" %}</span></td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-landmark text-secondary me-2"></i>
                                            {% trans "History" %}
                                        </div>
                                    </td>
                                    <td>Khalid Mahmoud</td>
                                    <td>4</td>
                                    <td>120</td>
                                    <td>
                                        <span class="badge bg-warning">75%</span>
                                    </td>
                                    <td><span class="badge bg-warning">{% trans "Average" %}</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upcoming Events -->
        <div class="col-lg-4 mb-4">
            <div class="card academic-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>{% trans "Upcoming Events" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{% trans "Mid-term Exams" %}</strong>
                                <br>
                                <small class="text-muted">{% trans "All Grades" %}</small>
                            </div>
                            <span class="badge bg-warning rounded-pill">{% trans "Next Week" %}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{% trans "Parent-Teacher Meeting" %}</strong>
                                <br>
                                <small class="text-muted">{% trans "Grade 1-3" %}</small>
                            </div>
                            <span class="badge bg-info rounded-pill">{% trans "Feb 20" %}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{% trans "Science Fair" %}</strong>
                                <br>
                                <small class="text-muted">{% trans "All Students" %}</small>
                            </div>
                            <span class="badge bg-success rounded-pill">{% trans "Mar 5" %}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{% trans "Final Exams" %}</strong>
                                <br>
                                <small class="text-muted">{% trans "All Grades" %}</small>
                            </div>
                            <span class="badge bg-danger rounded-pill">{% trans "May 15" %}</span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6 class="mb-3">{% trans "Academic Calendar" %}</h6>
                    <div class="small">
                        <div class="d-flex justify-content-between mb-2">
                            <span>{% trans "Current Semester" %}</span>
                            <span class="text-primary">{% trans "Spring 2025" %}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>{% trans "Days Remaining" %}</span>
                            <span class="text-warning">45 {% trans "days" %}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Academic Progress" %}</span>
                            <span class="text-success">65%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Academic Performance Chart
        const ctx = document.getElementById('performanceChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['{% trans "Mathematics" %}', '{% trans "Science" %}', '{% trans "English" %}', '{% trans "Arabic" %}', '{% trans "History" %}'],
                datasets: [{
                    label: '{% trans "Average Grade %" %}',
                    data: [85, 82, 78, 88, 75],
                    backgroundColor: [
                        '#667eea',
                        '#11998e',
                        '#fc466b',
                        '#fdbb2d',
                        '#764ba2'
                    ],
                    borderRadius: 10,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
