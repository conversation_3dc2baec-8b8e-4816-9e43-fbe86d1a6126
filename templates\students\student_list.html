{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Students" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .student-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
    }
    
    .student-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .student-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .search-box {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 10px 20px;
        transition: border-color 0.3s ease;
    }
    
    .search-box:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .filter-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        border: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-users text-primary me-2"></i>{% trans "Students Management" %}
                    </h2>
                    <p class="text-muted">{% trans "Manage student information, enrollment, and academic records" %}</p>
                </div>
                <div>
                    <a href="{% url 'students:add_student' %}" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>{% trans "Add New Student" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ students.count|default:0 }}</h3>
                    <p class="mb-0">{% trans "Total Students" %}</p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ active_students|default:0 }}</h3>
                    <p class="mb-0">{% trans "Active Students" %}</p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-plus fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ new_admissions|default:0 }}</h3>
                    <p class="mb-0">{% trans "New Admissions" %}</p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                    <h3 class="mb-1">{{ graduating_students|default:0 }}</h3>
                    <p class="mb-0">{% trans "Graduating Students" %}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filter Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-6">
                            <input type="text" name="search" class="form-control search-box" 
                                   placeholder="{% trans 'Search by name, ID, or admission number...' %}"
                                   value="{{ request.GET.search }}">
                        </div>
                        <div class="col-md-3">
                            <select name="grade" class="form-select">
                                <option value="">{% trans "All Grades" %}</option>
                                <!-- Add grade options here -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>{% trans "Search" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card filter-card">
                <div class="card-body text-center">
                    <h5 class="mb-2">{% trans "Quick Filters" %}</h5>
                    <div class="btn-group-vertical w-100" role="group">
                        <a href="?filter=active" class="btn btn-outline-light btn-sm">{% trans "Active Students" %}</a>
                        <a href="?filter=new" class="btn btn-outline-light btn-sm">{% trans "New Admissions" %}</a>
                        <a href="?filter=pending" class="btn btn-outline-light btn-sm">{% trans "Pending Fees" %}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Students List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>{% trans "Students List" %}
                        </h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm active">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if students %}
                        <div class="row">
                            {% for student in students %}
                                <div class="col-xl-4 col-md-6 mb-4">
                                    <div class="card student-card h-100">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="student-avatar me-3">
                                                    {{ student.first_name|first }}{{ student.last_name|first }}
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1">
                                                        <a href="{% url 'students:detail' student.pk %}" class="text-decoration-none">
                                                            {{ student.first_name }} {{ student.last_name }}
                                                        </a>
                                                    </h6>
                                                    <small class="text-muted">ID: {{ student.student_id|default:"N/A" }}</small>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="{% url 'students:detail' student.pk %}">
                                                            <i class="fas fa-eye me-2"></i>{% trans "View Details" %}
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="{% url 'students:edit' student.pk %}">
                                                            <i class="fas fa-edit me-2"></i>{% trans "Edit" %}
                                                        </a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="{% url 'students:delete' student.pk %}">
                                                            <i class="fas fa-trash me-2"></i>{% trans "Delete" %}
                                                        </a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            
                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <small class="text-muted d-block">{% trans "Class" %}</small>
                                                    <strong>{{ student.current_class|default:"N/A" }}</strong>
                                                </div>
                                                <div class="col-4">
                                                    <small class="text-muted d-block">{% trans "Grade" %}</small>
                                                    <strong>{{ student.current_class.grade|default:"N/A" }}</strong>
                                                </div>
                                                <div class="col-4">
                                                    <small class="text-muted d-block">{% trans "Status" %}</small>
                                                    <span class="badge bg-success">{% trans "Active" %}</span>
                                                </div>
                                            </div>
                                            
                                            <hr>
                                            
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    {% trans "Admitted" %}: {{ student.admission_date|date:"M Y"|default:"N/A" }}
                                                </small>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="{% url 'students:detail' student.pk %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'students:edit' student.pk %}" class="btn btn-outline-success btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Pagination -->
                        {% if is_paginated %}
                            <nav aria-label="Students pagination">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1">{% trans "First" %}</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                        </li>
                                    {% endif %}
                                    
                                    <li class="page-item active">
                                        <span class="page-link">
                                            {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>
                                    
                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %}</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-5x text-muted mb-3"></i>
                            <h4 class="text-muted">{% trans "No students found" %}</h4>
                            <p class="text-muted">{% trans "Start by adding your first student to the system." %}</p>
                            <a href="{% url 'students:add_student' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Add First Student" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any JavaScript functionality here
    console.log('Students list loaded');
});
</script>
{% endblock %}
