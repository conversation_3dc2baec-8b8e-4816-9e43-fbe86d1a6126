from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView, ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Count, Avg, Sum
from django.db import models
from django.http import HttpResponse, JsonResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import datetime, timedelta
from .models import (
    Department, Position, Employee, AttendanceRecord, LeaveType, LeaveRequest,
    PayrollPeriod, Payroll, PerformanceEvaluation, EmployeeDocument
)
from .forms import (
    DepartmentForm, PositionForm, EmployeeForm, AttendanceRecordForm, LeaveRequestForm,
    PayrollForm, PerformanceEvaluationForm, EmployeeDocumentForm, LeaveTypeForm,
    PayrollPeriodForm, QuickAttendanceForm
)

def placeholder_view(request):
    return HttpResponse("HR module - Coming soon!")

# HR Dashboard
class HRDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Employee statistics
        context['total_employees'] = Employee.objects.count()
        context['active_employees'] = Employee.objects.filter(employment_status='active').count()

        # Attendance statistics for current month
        current_month = timezone.now().replace(day=1)
        next_month = (current_month + timedelta(days=32)).replace(day=1)

        total_attendance_records = AttendanceRecord.objects.filter(
            date__gte=current_month,
            date__lt=next_month
        ).count()

        present_records = AttendanceRecord.objects.filter(
            date__gte=current_month,
            date__lt=next_month,
            status__in=['present', 'late']
        ).count()

        context['attendance_rate'] = (present_records / total_attendance_records * 100) if total_attendance_records > 0 else 0

        # Leave requests
        context['pending_leave_requests'] = LeaveRequest.objects.filter(status='pending').count()
        context['total_leave_requests'] = LeaveRequest.objects.filter(
            created_at__gte=current_month
        ).count()

        # Payroll (mock data for now)
        context['monthly_payroll'] = Payroll.objects.filter(
            period__start_date__gte=current_month,
            period__start_date__lt=next_month
        ).aggregate(total=models.Sum('net_salary'))['total'] or 0

        # Recent activities (mock data)
        context['recent_activities'] = []

        # Upcoming events (mock data)
        context['upcoming_events'] = []

        # Alerts
        context['expiring_documents'] = EmployeeDocument.objects.filter(
            expiry_date__lte=timezone.now().date() + timedelta(days=30),
            expiry_date__isnull=False
        ).count()

        context['overdue_evaluations'] = 0  # Will implement later

        # Chart data (last 6 months attendance)
        months = []
        attendance_data = []

        for i in range(6):
            month_start = (current_month - timedelta(days=i*30)).replace(day=1)
            month_end = (month_start + timedelta(days=32)).replace(day=1)

            months.append(month_start.strftime('%b %Y'))

            month_total = AttendanceRecord.objects.filter(
                date__gte=month_start,
                date__lt=month_end
            ).count()

            month_present = AttendanceRecord.objects.filter(
                date__gte=month_start,
                date__lt=month_end,
                status__in=['present', 'late']
            ).count()

            rate = (month_present / month_total * 100) if month_total > 0 else 0
            attendance_data.append(rate)

        context['attendance_labels'] = list(reversed(months))
        context['attendance_data'] = list(reversed(attendance_data))

        # Department distribution
        departments = Department.objects.annotate(
            employee_count=Count('positions__employees')
        ).filter(employee_count__gt=0)

        context['department_labels'] = [dept.name for dept in departments]
        context['department_data'] = [dept.employee_count for dept in departments]

        return context

# Employee Management
class EmployeeListView(LoginRequiredMixin, ListView):
    model = Employee
    template_name = 'hr/employees.html'
    context_object_name = 'employees'
    paginate_by = 20

    def get_queryset(self):
        queryset = Employee.objects.select_related('user', 'position', 'position__department').all()
        search = self.request.GET.get('search')
        department = self.request.GET.get('department')
        status = self.request.GET.get('status')

        if search:
            queryset = queryset.filter(
                Q(user__first_name__icontains=search) |
                Q(user__last_name__icontains=search) |
                Q(employee_id__icontains=search)
            )
        if department:
            queryset = queryset.filter(position__department_id=department)
        if status:
            queryset = queryset.filter(employment_status=status)

        return queryset.order_by('user__first_name', 'user__last_name')


class EmployeeCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Employee
    form_class = EmployeeForm
    template_name = 'hr/employee_form.html'
    permission_required = 'hr.add_employee'
    success_url = reverse_lazy('hr:employees')

    def form_valid(self, form):
        messages.success(self.request, _('Employee created successfully!'))
        return super().form_valid(form)


class EmployeeDetailView(LoginRequiredMixin, DetailView):
    model = Employee
    template_name = 'hr/employee_detail.html'
    context_object_name = 'employee'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        employee = self.get_object()

        # Recent attendance
        context['recent_attendance'] = AttendanceRecord.objects.filter(
            employee=employee
        ).order_by('-date')[:10]

        # Leave requests
        context['recent_leave_requests'] = LeaveRequest.objects.filter(
            employee=employee
        ).order_by('-created_at')[:5]

        # Documents
        context['documents'] = EmployeeDocument.objects.filter(
            employee=employee
        ).order_by('-created_at')[:5]

        return context


class EmployeeUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Employee
    form_class = EmployeeForm
    template_name = 'hr/employee_form.html'
    permission_required = 'hr.change_employee'
    success_url = reverse_lazy('hr:employees')

    def form_valid(self, form):
        messages.success(self.request, _('Employee updated successfully!'))
        return super().form_valid(form)


class EmployeeDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView):
    model = Employee
    template_name = 'hr/employee_confirm_delete.html'
    permission_required = 'hr.delete_employee'
    success_url = reverse_lazy('hr:employees')

    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Employee deleted successfully!'))
        return super().delete(request, *args, **kwargs)

# Department Management
class DepartmentListView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/departments.html'

class DepartmentCreateView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/department_form.html'

class DepartmentUpdateView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/department_form.html'

# Position Management
class PositionListView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/positions.html'

class PositionCreateView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/position_form.html'

class PositionUpdateView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/position_form.html'

# General Settings
class RegistrationFilesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/registration_files.html'

class EmployeeAffairsSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/affairs_settings.html'

class WorkSystemView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/work_system.html'

class AttendanceDevicesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/attendance_devices.html'

class AttendanceDevices2View(LoginRequiredMixin, TemplateView):
    template_name = 'hr/attendance_devices_2.html'

class PermissionTypesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/permission_types.html'

class HolidayTypesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/holiday_types.html'

class AllowanceTypesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/allowance_types.html'

class DeductionTypesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/deduction_types.html'

class PublicHolidaysView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/public_holidays.html'

# Work System Settings
class DeductionRulesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/deduction_rules.html'

class ExtraTimeRulesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/extra_time_rules.html'

class RewardsRulesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/rewards_rules.html'

class PenaltiesRulesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/penalties_rules.html'

# Attendance and Presence
class AttendanceView(LoginRequiredMixin, ListView):
    model = AttendanceRecord
    template_name = 'hr/attendance.html'
    context_object_name = 'attendance_records'
    paginate_by = 20

    def get_queryset(self):
        queryset = AttendanceRecord.objects.select_related('employee', 'employee__user').all()
        date = self.request.GET.get('date')
        employee = self.request.GET.get('employee')
        status = self.request.GET.get('status')

        if date:
            queryset = queryset.filter(date=date)
        if employee:
            queryset = queryset.filter(employee_id=employee)
        if status:
            queryset = queryset.filter(status=status)

        return queryset.order_by('-date', 'employee__user__first_name')


class ManualAttendanceView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = AttendanceRecord
    form_class = AttendanceRecordForm
    template_name = 'hr/manual_attendance.html'
    permission_required = 'hr.add_attendancerecord'
    success_url = reverse_lazy('hr:attendance')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        form.instance.is_manual = True
        messages.success(self.request, _('Attendance record created successfully!'))
        return super().form_valid(form)


class AttendanceReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/attendance_reports.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get date range from request
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')

        if start_date and end_date:
            # Generate attendance report
            attendance_records = AttendanceRecord.objects.filter(
                date__gte=start_date,
                date__lte=end_date
            ).select_related('employee', 'employee__user')

            context['attendance_records'] = attendance_records
            context['start_date'] = start_date
            context['end_date'] = end_date

        return context


class AttendanceSummaryView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/attendance_summary.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Monthly attendance summary
        current_month = timezone.now().replace(day=1)
        next_month = (current_month + timedelta(days=32)).replace(day=1)

        context['monthly_stats'] = AttendanceRecord.objects.filter(
            date__gte=current_month,
            date__lt=next_month
        ).values('status').annotate(count=Count('id'))

        return context


class AttendanceDevicesDataView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/attendance_devices_data.html'

# Permissions and Leaves
class PermissionsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/permissions.html'

class AddPermissionView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/add_permission.html'

class ApprovePermissionsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/approve_permissions.html'

class PermissionReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/permission_reports.html'


# Leave Management
class LeaveRequestsView(LoginRequiredMixin, ListView):
    model = LeaveRequest
    template_name = 'hr/leave_requests.html'
    context_object_name = 'leave_requests'
    paginate_by = 20

    def get_queryset(self):
        queryset = LeaveRequest.objects.select_related('employee', 'employee__user', 'leave_type', 'approved_by').all()
        status = self.request.GET.get('status')
        employee = self.request.GET.get('employee')
        leave_type = self.request.GET.get('leave_type')

        if status:
            queryset = queryset.filter(status=status)
        if employee:
            queryset = queryset.filter(employee_id=employee)
        if leave_type:
            queryset = queryset.filter(leave_type_id=leave_type)

        return queryset.order_by('-created_at')


class LeaveRequestCreateView(LoginRequiredMixin, CreateView):
    model = LeaveRequest
    form_class = LeaveRequestForm
    template_name = 'hr/leave_request_form.html'
    success_url = reverse_lazy('hr:leave_requests')

    def form_valid(self, form):
        messages.success(self.request, _('Leave request submitted successfully!'))
        return super().form_valid(form)


class LeaveBalanceView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/leave_balance.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Calculate leave balances for all employees
        employees = Employee.objects.filter(employment_status='active')
        leave_types = LeaveType.objects.all()

        balances = []
        for employee in employees:
            employee_balance = {'employee': employee, 'balances': {}}
            for leave_type in leave_types:
                used_days = LeaveRequest.objects.filter(
                    employee=employee,
                    leave_type=leave_type,
                    status='approved'
                ).aggregate(
                    total=models.Sum('duration_days')
                )['total'] or 0

                remaining = leave_type.max_days_per_year - used_days
                employee_balance['balances'][leave_type.name] = {
                    'total': leave_type.max_days_per_year,
                    'used': used_days,
                    'remaining': remaining
                }
            balances.append(employee_balance)

        context['employee_balances'] = balances
        context['leave_types'] = leave_types

        return context

# Holidays and Vacations
class HolidaysView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/holidays.html'

class VacationRequestsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/vacation_requests.html'

class VacationBalanceView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/vacation_balance.html'

# Payroll
class PayrollView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/payroll.html'

class GeneratePayrollView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/generate_payroll.html'

class PayrollReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/payroll_reports.html'

class SalaryStructureView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/salary_structure.html'

class AllowancesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/allowances.html'

class DeductionsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/deductions.html'

class BonusesView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/bonuses.html'

# Performance and Evaluation
class PerformanceView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/performance.html'

# HR Reports
class HRReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/reports.html'

class EmployeeReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/employee_reports.html'

class EmployeeAttendanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/employee_attendance_report.html'

class LeaveReportView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/leave_report.html'

class EmployeePerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/employee_performance_report.html'

class PerformanceReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'hr/performance_reports.html'
