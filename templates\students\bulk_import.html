{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}{% trans "Bulk Import Students" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .import-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .import-card:hover {
        transform: translateY(-2px);
    }
    .upload-area {
        border: 2px dashed #007bff;
        border-radius: 15px;
        padding: 3rem;
        text-align: center;
        background: #f8f9fa;
        transition: all 0.3s;
        cursor: pointer;
    }
    .upload-area:hover {
        border-color: #0056b3;
        background: #e3f2fd;
    }
    .upload-area.dragover {
        border-color: #28a745;
        background: #d4edda;
    }
    .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 2rem;
    }
    .step {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 1rem;
        font-weight: bold;
        position: relative;
    }
    .step.active {
        background: #007bff;
        color: white;
    }
    .step.completed {
        background: #28a745;
        color: white;
    }
    .step::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 100%;
        width: 2rem;
        height: 2px;
        background: #e9ecef;
        transform: translateY(-50%);
    }
    .step:last-child::after {
        display: none;
    }
    .step.completed::after {
        background: #28a745;
    }
    .preview-table {
        max-height: 400px;
        overflow-y: auto;
    }
    .error-row {
        background-color: #f8d7da !important;
    }
    .success-row {
        background-color: #d4edda !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-upload text-primary me-2"></i>{% trans "Bulk Import Students" %}
                    </h2>
                    <p class="text-muted">{% trans "Import multiple students from Excel or CSV files" %}</p>
                </div>
                <div>
                    <a href="{% url 'students:list' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Students" %}
                    </a>
                    <a href="#" class="btn btn-success" id="downloadTemplate">
                        <i class="fas fa-download me-2"></i>{% trans "Download Template" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Step Indicator -->
    <div class="step-indicator">
        <div class="step active" id="step1">1</div>
        <div class="step" id="step2">2</div>
        <div class="step" id="step3">3</div>
        <div class="step" id="step4">4</div>
    </div>

    <!-- Step 1: File Upload -->
    <div class="step-content" id="stepContent1">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card import-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-upload me-2"></i>{% trans "Step 1: Upload File" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="uploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h4>{% trans "Drag and drop your file here" %}</h4>
                            <p class="text-muted">{% trans "or click to browse files" %}</p>
                            <p class="small text-muted">{% trans "Supported formats: .xlsx, .xls, .csv (Max size: 10MB)" %}</p>
                            <input type="file" id="fileInput" class="d-none" accept=".xlsx,.xls,.csv">
                        </div>
                        
                        <div class="mt-4" id="fileInfo" style="display: none;">
                            <div class="alert alert-info">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file-excel fa-2x me-3"></i>
                                    <div>
                                        <h6 class="mb-1" id="fileName"></h6>
                                        <small id="fileSize"></small>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger ms-auto" id="removeFile">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid mt-4">
                            <button type="button" class="btn btn-primary" id="nextStep1" disabled>
                                {% trans "Next: Validate Data" %} <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 2: Data Validation -->
    <div class="step-content d-none" id="stepContent2">
        <div class="row">
            <div class="col-12">
                <div class="card import-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>{% trans "Step 2: Data Validation" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="text-success" id="validCount">0</h3>
                                    <p class="mb-0">{% trans "Valid Records" %}</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="text-danger" id="errorCount">0</h3>
                                    <p class="mb-0">{% trans "Errors Found" %}</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="text-info" id="totalCount">0</h3>
                                    <p class="mb-0">{% trans "Total Records" %}</p>
                                </div>
                            </div>
                        </div>

                        <div class="preview-table">
                            <table class="table table-sm table-hover" id="previewTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "Row" %}</th>
                                        <th>{% trans "Student ID" %}</th>
                                        <th>{% trans "First Name" %}</th>
                                        <th>{% trans "Last Name" %}</th>
                                        <th>{% trans "Email" %}</th>
                                        <th>{% trans "Class" %}</th>
                                        <th>{% trans "Status" %}</th>
                                    </tr>
                                </thead>
                                <tbody id="previewTableBody">
                                    <!-- Data will be populated here -->
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-outline-secondary" id="prevStep2">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Previous" %}
                            </button>
                            <button type="button" class="btn btn-primary" id="nextStep2">
                                {% trans "Next: Configure Import" %} <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 3: Import Configuration -->
    <div class="step-content d-none" id="stepContent3">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card import-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>{% trans "Step 3: Import Configuration" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="importConfigForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="defaultClass" class="form-label">{% trans "Default Class" %}</label>
                                    <select class="form-select" id="defaultClass">
                                        <option value="">{% trans "Select default class" %}</option>
                                        <option value="1A">Grade 1-A</option>
                                        <option value="1B">Grade 1-B</option>
                                        <option value="2A">Grade 2-A</option>
                                        <option value="2B">Grade 2-B</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="academicYear" class="form-label">{% trans "Academic Year" %}</label>
                                    <select class="form-select" id="academicYear">
                                        <option value="2024-2025">2024-2025</option>
                                        <option value="2025-2026">2025-2026</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="skipErrors" checked>
                                        <label class="form-check-label" for="skipErrors">
                                            {% trans "Skip records with errors" %}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="sendWelcomeEmail">
                                        <label class="form-check-label" for="sendWelcomeEmail">
                                            {% trans "Send welcome email to students" %}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="generateIds" checked>
                                        <label class="form-check-label" for="generateIds">
                                            {% trans "Auto-generate student IDs if missing" %}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-outline-secondary" id="prevStep3">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Previous" %}
                            </button>
                            <button type="button" class="btn btn-success" id="startImport">
                                <i class="fas fa-play me-2"></i>{% trans "Start Import" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 4: Import Progress -->
    <div class="step-content d-none" id="stepContent4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card import-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tasks me-2"></i>{% trans "Step 4: Import Progress" %}
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-4">
                            <div class="spinner-border text-primary" role="status" id="importSpinner">
                                <span class="visually-hidden">{% trans "Loading..." %}</span>
                            </div>
                        </div>
                        
                        <h4 id="importStatus">{% trans "Importing students..." %}</h4>
                        <p class="text-muted" id="importProgress">{% trans "Please wait while we process your data" %}</p>
                        
                        <div class="progress mb-4" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%" id="progressBar">0%</div>
                        </div>

                        <div id="importResults" class="d-none">
                            <div class="row">
                                <div class="col-md-4">
                                    <h3 class="text-success" id="successCount">0</h3>
                                    <p>{% trans "Successfully Imported" %}</p>
                                </div>
                                <div class="col-md-4">
                                    <h3 class="text-danger" id="failedCount">0</h3>
                                    <p>{% trans "Failed to Import" %}</p>
                                </div>
                                <div class="col-md-4">
                                    <h3 class="text-info" id="processedCount">0</h3>
                                    <p>{% trans "Total Processed" %}</p>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
                                <a href="{% url 'students:list' %}" class="btn btn-primary">
                                    <i class="fas fa-list me-2"></i>{% trans "View Students" %}
                                </a>
                                <button type="button" class="btn btn-outline-secondary" id="downloadReport">
                                    <i class="fas fa-download me-2"></i>{% trans "Download Report" %}
                                </button>
                                <button type="button" class="btn btn-success" id="importAnother">
                                    <i class="fas fa-plus me-2"></i>{% trans "Import Another File" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const removeFile = document.getElementById('removeFile');
    const nextStep1 = document.getElementById('nextStep1');
    
    let currentStep = 1;
    let uploadedFile = null;

    // File upload handling
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);
    removeFile.addEventListener('click', clearFile);

    function handleDragOver(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    }

    function handleDrop(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            processFile(files[0]);
        }
    }

    function handleFileSelect(e) {
        const files = e.target.files;
        if (files.length > 0) {
            processFile(files[0]);
        }
    }

    function processFile(file) {
        if (validateFile(file)) {
            uploadedFile = file;
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'block';
            nextStep1.disabled = false;
        }
    }

    function validateFile(file) {
        const allowedTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
            'text/csv'
        ];
        
        if (!allowedTypes.includes(file.type)) {
            alert('{% trans "Please select a valid Excel or CSV file" %}');
            return false;
        }
        
        if (file.size > 10 * 1024 * 1024) { // 10MB
            alert('{% trans "File size must be less than 10MB" %}');
            return false;
        }
        
        return true;
    }

    function clearFile() {
        uploadedFile = null;
        fileInput.value = '';
        fileInfo.style.display = 'none';
        nextStep1.disabled = true;
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Step navigation
    document.getElementById('nextStep1').addEventListener('click', () => goToStep(2));
    document.getElementById('prevStep2').addEventListener('click', () => goToStep(1));
    document.getElementById('nextStep2').addEventListener('click', () => goToStep(3));
    document.getElementById('prevStep3').addEventListener('click', () => goToStep(2));
    document.getElementById('startImport').addEventListener('click', startImport);
    document.getElementById('importAnother').addEventListener('click', () => goToStep(1));

    function goToStep(step) {
        // Hide current step
        document.getElementById(`stepContent${currentStep}`).classList.add('d-none');
        document.getElementById(`step${currentStep}`).classList.remove('active');
        
        // Show new step
        document.getElementById(`stepContent${step}`).classList.remove('d-none');
        document.getElementById(`step${step}`).classList.add('active');
        
        // Mark previous steps as completed
        for (let i = 1; i < step; i++) {
            document.getElementById(`step${i}`).classList.add('completed');
        }
        
        currentStep = step;
        
        if (step === 2) {
            validateData();
        }
    }

    function validateData() {
        // Simulate data validation
        setTimeout(() => {
            document.getElementById('validCount').textContent = '45';
            document.getElementById('errorCount').textContent = '3';
            document.getElementById('totalCount').textContent = '48';
            
            // Populate preview table with sample data
            const tbody = document.getElementById('previewTableBody');
            tbody.innerHTML = `
                <tr class="success-row">
                    <td>1</td>
                    <td>STU001</td>
                    <td>أحمد</td>
                    <td>محمد</td>
                    <td><EMAIL></td>
                    <td>1-A</td>
                    <td><span class="badge bg-success">Valid</span></td>
                </tr>
                <tr class="error-row">
                    <td>2</td>
                    <td></td>
                    <td>فاطمة</td>
                    <td>علي</td>
                    <td>invalid-email</td>
                    <td>2-B</td>
                    <td><span class="badge bg-danger">Invalid Email</span></td>
                </tr>
                <tr class="success-row">
                    <td>3</td>
                    <td>STU003</td>
                    <td>محمد</td>
                    <td>أحمد</td>
                    <td><EMAIL></td>
                    <td>1-B</td>
                    <td><span class="badge bg-success">Valid</span></td>
                </tr>
            `;
        }, 1000);
    }

    function startImport() {
        goToStep(4);
        
        // Simulate import process
        let progress = 0;
        const progressBar = document.getElementById('progressBar');
        const importProgress = document.getElementById('importProgress');
        
        const interval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress > 100) progress = 100;
            
            progressBar.style.width = progress + '%';
            progressBar.textContent = Math.round(progress) + '%';
            importProgress.textContent = `Processing record ${Math.round(progress * 0.48)} of 48`;
            
            if (progress >= 100) {
                clearInterval(interval);
                completeImport();
            }
        }, 500);
    }

    function completeImport() {
        document.getElementById('importSpinner').style.display = 'none';
        document.getElementById('importStatus').textContent = '{% trans "Import Completed!" %}';
        document.getElementById('importProgress').textContent = '{% trans "All records have been processed" %}';
        
        document.getElementById('successCount').textContent = '45';
        document.getElementById('failedCount').textContent = '3';
        document.getElementById('processedCount').textContent = '48';
        
        document.getElementById('importResults').classList.remove('d-none');
        document.getElementById('step4').classList.add('completed');
    }

    // Download template
    document.getElementById('downloadTemplate').addEventListener('click', function(e) {
        e.preventDefault();
        alert('{% trans "Template download would start here" %}');
    });

    // Download report
    document.getElementById('downloadReport').addEventListener('click', function() {
        alert('{% trans "Import report download would start here" %}');
    });
});
</script>
{% endblock %}
