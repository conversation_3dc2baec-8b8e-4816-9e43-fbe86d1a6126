Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 17325
"GET /static/admin/css/base.css HTTP/1.1" 200 22120
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
"GET /static/admin/css/login.css HTTP/1.1" 200 951
"GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
"GET /static/admin/js/theme.js HTTP/1.1" 200 1653
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 29444
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 14803
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4692
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 17026
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 17492
Watching for file changes with StatReloader
Internal Server Error: /admin/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1523, in get_connection
    connection = self._available_connections.pop()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\admin\sites.py", line 449, in login
    return LoginView.as_view(**defaults)(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\debug.py", line 143, in sensitive_post_parameters_wrapper
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\views.py", line 89, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\base.py", line 144, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\views.py", line 108, in form_valid
    auth_login(self.request, form.get_user())
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\__init__.py", line 187, in login
    request.session.cycle_key()
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\base.py", line 432, in cycle_key
    self.create()
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 55, in create
    self._session_key = self._get_new_session_key()
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\base.py", line 196, in _get_new_session_key
    if not self.exists(session_key):
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 117, in exists
    bool(session_key) and (self.cache_key_prefix + session_key) in self._cache
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\base.py", line 301, in __contains__
    return self.has_key(key)
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\debug_toolbar\panels\cache.py", line 42, in wrapper
    return panel._record_call(cache, name, original_method, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\debug_toolbar\panels\cache.py", line 151, in _record_call
    value = original_method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\redis.py", line 211, in has_key
    return self._cache.has_key(key)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\redis.py", line 131, in has_key
    return bool(client.exists(key))
                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\commands\core.py", line 1736, in exists
    return self.execute_command("EXISTS", *names, keys=names)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1525, in get_connection
    connection = self.make_connection()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1571, in make_connection
    return self.connection_class(**self.connection_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 747, in __init__
    super().__init__(**kwargs)
TypeError: AbstractConnection.__init__() got an unexpected keyword argument 'CLIENT_CLASS'
"POST /admin/login/?next=/admin/ HTTP/1.1" 500 239671
Internal Server Error: /admin/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1523, in get_connection
    connection = self._available_connections.pop()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\admin\sites.py", line 449, in login
    return LoginView.as_view(**defaults)(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\debug.py", line 143, in sensitive_post_parameters_wrapper
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\views.py", line 89, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\base.py", line 144, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\views.py", line 108, in form_valid
    auth_login(self.request, form.get_user())
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\__init__.py", line 187, in login
    request.session.cycle_key()
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\base.py", line 432, in cycle_key
    self.create()
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 55, in create
    self._session_key = self._get_new_session_key()
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\base.py", line 196, in _get_new_session_key
    if not self.exists(session_key):
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 117, in exists
    bool(session_key) and (self.cache_key_prefix + session_key) in self._cache
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\base.py", line 301, in __contains__
    return self.has_key(key)
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\debug_toolbar\panels\cache.py", line 42, in wrapper
    return panel._record_call(cache, name, original_method, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\debug_toolbar\panels\cache.py", line 151, in _record_call
    value = original_method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\redis.py", line 211, in has_key
    return self._cache.has_key(key)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\redis.py", line 131, in has_key
    return bool(client.exists(key))
                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\commands\core.py", line 1736, in exists
    return self.execute_command("EXISTS", *names, keys=names)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1525, in get_connection
    connection = self.make_connection()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1571, in make_connection
    return self.connection_class(**self.connection_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 747, in __init__
    super().__init__(**kwargs)
TypeError: AbstractConnection.__init__() got an unexpected keyword argument 'CLIENT_CLASS'
"POST /admin/login/?next=/admin/ HTTP/1.1" 500 239671
Internal Server Error: /admin/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1523, in get_connection
    connection = self._available_connections.pop()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\admin\sites.py", line 449, in login
    return LoginView.as_view(**defaults)(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\debug.py", line 143, in sensitive_post_parameters_wrapper
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\views.py", line 89, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\base.py", line 144, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\views.py", line 108, in form_valid
    auth_login(self.request, form.get_user())
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\__init__.py", line 187, in login
    request.session.cycle_key()
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\base.py", line 432, in cycle_key
    self.create()
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 55, in create
    self._session_key = self._get_new_session_key()
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\base.py", line 196, in _get_new_session_key
    if not self.exists(session_key):
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 117, in exists
    bool(session_key) and (self.cache_key_prefix + session_key) in self._cache
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\base.py", line 301, in __contains__
    return self.has_key(key)
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\debug_toolbar\panels\cache.py", line 42, in wrapper
    return panel._record_call(cache, name, original_method, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\debug_toolbar\panels\cache.py", line 151, in _record_call
    value = original_method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\redis.py", line 211, in has_key
    return self._cache.has_key(key)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\redis.py", line 131, in has_key
    return bool(client.exists(key))
                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\commands\core.py", line 1736, in exists
    return self.execute_command("EXISTS", *names, keys=names)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1525, in get_connection
    connection = self.make_connection()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1571, in make_connection
    return self.connection_class(**self.connection_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 747, in __init__
    super().__init__(**kwargs)
TypeError: AbstractConnection.__init__() got an unexpected keyword argument 'CLIENT_CLASS'
"POST /admin/login/?next=/admin/ HTTP/1.1" 500 239671
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
D:\Python-projects\school-onlinepro\school_erp\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\school_erp\settings.py changed, reloading.
Watching for file changes with StatReloader
Not Found: /accounts
"GET /accounts HTTP/1.1" 404 17014
"GET /accounts/login HTTP/1.1" 301 0
"GET /accounts/login/ HTTP/1.1" 200 21598
"GET /static/css/style.css HTTP/1.1" 200 6824
"GET /static/js/main.js HTTP/1.1" 200 11978
"GET /static/js/sw.js HTTP/1.1" 200 9601
"GET / HTTP/1.1" 302 0
"GET /accounts/dashboard/ HTTP/1.1" 302 0
"GET /accounts/login/ HTTP/1.1" 200 21598
"GET /accounts/login/?next=/accounts/dashboard/ HTTP/1.1" 200 21624
"GET /accounts/login/ HTTP/1.1" 200 21598
D:\Python-projects\school-onlinepro\school_erp\settings.py changed, reloading.
Watching for file changes with StatReloader
"POST /accounts/login/ HTTP/1.1" 302 0
"GET /accounts/dashboard/ HTTP/1.1" 200 21692
"GET /accounts/dashboard/ HTTP/1.1" 200 21258
D:\Python-projects\school-onlinepro\school_erp\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"HEAD / HTTP/1.1" 302 0
"HEAD /accounts/login/ HTTP/1.1" 200 0
- Broken pipe from ('127.0.0.1', 57692)
"GET / HTTP/1.1" 302 0
"GET /accounts/dashboard/ HTTP/1.1" 200 21258
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
"HEAD /admin/ HTTP/1.1" 302 0
"HEAD /admin/login/?next=/admin/ HTTP/1.1" 200 0
- Broken pipe from ('127.0.0.1', 57714)
"GET /accounts/dashboard/ HTTP/1.1" 200 21258
Watching for file changes with StatReloader
"GET /accounts/dashboard/ HTTP/1.1" 200 21259
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
"GET /admin/ HTTP/1.1" 200 29580
"GET /static/admin/css/base.css HTTP/1.1" 304 0
"GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive.css HTTP/1.1" 304 0
"GET /static/admin/js/theme.js HTTP/1.1" 304 0
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
"GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
"GET / HTTP/1.1" 302 0
"GET /accounts/dashboard/ HTTP/1.1" 200 21258
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
Internal Server Error: /accounts/profile/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/profile.html
"GET /accounts/profile/ HTTP/1.1" 500 100063
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\students\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 302 0
"GET /accounts/login/ HTTP/1.1" 200 21598
"GET / HTTP/1.1" 302 0
"GET /accounts/dashboard/ HTTP/1.1" 200 22811
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
Internal Server Error: /reports/students/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: reports/students.html
"GET /reports/students/ HTTP/1.1" 500 100076
Watching for file changes with StatReloader
Internal Server Error: /reports/financial/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: reports/financial.html
"GET /reports/financial/ HTTP/1.1" 500 100429
Internal Server Error: /reports/attendance/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: reports/attendance.html
"GET /reports/attendance/ HTTP/1.1" 500 100462
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /reports/ HTTP/1.1" 200 32850
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
"GET /reports/attendance/ HTTP/1.1" 200 35184
Internal Server Error: /reports/attendance/daily/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: reports/daily_attendance.html
"GET /reports/attendance/daily/ HTTP/1.1" 500 100394
Internal Server Error: /reports/attendance/class-wise/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: reports/class_wise_attendance.html
"GET /reports/attendance/class-wise/ HTTP/1.1" 500 100591
Internal Server Error: /reports/attendance/student-wise/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: reports/student_wise_attendance.html
"GET /reports/attendance/student-wise/ HTTP/1.1" 500 100671
Internal Server Error: /reports/attendance/late-arrivals/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: reports/late_arrivals.html
"GET /reports/attendance/late-arrivals/ HTTP/1.1" 500 100384
"GET /reports/financial/ HTTP/1.1" 200 31540
"GET /reports/financial/revenue/ HTTP/1.1" 200 26323
Watching for file changes with StatReloader
"GET /reports/attendance/absentees/ HTTP/1.1" 200 32631
"GET /reports/attendance/daily/ HTTP/1.1" 200 32669
"GET /accounts/dashboard/ HTTP/1.1" 200 22811
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /reports/custom/builder/ HTTP/1.1" 200 34530
"GET /reports/hr/ HTTP/1.1" 200 32077
"GET /reports/hr/payroll/ HTTP/1.1" 200 32071
"GET /reports/hr/employee/ HTTP/1.1" 200 33779
Internal Server Error: /reports/hr/attendance/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: reports/employee_attendance.html
"GET /reports/hr/attendance/ HTTP/1.1" 500 100446
Internal Server Error: /reports/hr/performance/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: reports/employee_performance.html
"GET /reports/hr/performance/ HTTP/1.1" 500 100486
"GET /reports/academic/ HTTP/1.1" 200 32248
"GET /reports/academic/grades/ HTTP/1.1" 200 24069
"GET /reports/students/classes/ HTTP/1.1" 200 33873
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 17022
Internal Server Error: /reports/hr/performance/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: reports/employee_performance.html
"GET /reports/hr/performance/ HTTP/1.1" 500 100622
Internal Server Error: /reports/academic/curriculum-progress/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: reports/curriculum_progress.html
"GET /reports/academic/curriculum-progress/ HTTP/1.1" 500 100602
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 17023
"GET /reports/students/classes/ HTTP/1.1" 200 33873
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 17022
"GET /accounts/dashboard/ HTTP/1.1" 200 22811
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 17022
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 17023
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 17023
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 17023
"GET /reports/academic/curriculum-progress/ HTTP/1.1" 200 36856
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
"GET /reports/hr/performance/ HTTP/1.1" 200 43111
Not Found: /students/
"GET /students/ HTTP/1.1" 404 16866
Not Found: /students/dashboard/
"GET /students/dashboard/ HTTP/1.1" 404 16906
"GET /finance/ HTTP/1.1" 200 32059
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
Internal Server Error: /finance/fees-payment/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 1035, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'widget_tweaks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 42, in select_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 1097, in load
    lib = find_library(parser, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 1037, in find_library
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'widget_tweaks' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
cache
crispy_forms_field
crispy_forms_filters
crispy_forms_tags
crispy_forms_utils
debugger_tags
highlighting
i18n
import_export_tags
indent_text
l10n
log
rest_framework
static
syntax_color
tz
widont
"GET /finance/fees-payment/ HTTP/1.1" **********
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
Internal Server Error: /hr/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'leaves' not found. 'leaves' is not a valid view function or pattern name.
"GET /hr/ HTTP/1.1" 500 186065
Internal Server Error: /finance/account-statement/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: finance/account_statement.html
"GET /finance/account-statement/ HTTP/1.1" 500 100411
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /academics/ HTTP/1.1" 200 40246
Internal Server Error: /academics/schedules/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: academics/schedules.html
"GET /academics/schedules/ HTTP/1.1" 500 100179
Internal Server Error: /academics/teachers/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: academics/teachers.html
Internal Server Error: /academics/teachers/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: academics/teachers.html
- Broken pipe from ('127.0.0.1', 58414)
"GET /academics/teachers/ HTTP/1.1" 500 100139
Not Found: /students/
"GET /students/ HTTP/1.1" 404 16866
D:\Python-projects\school-onlinepro\school_erp\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\students\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\students\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /students/ HTTP/1.1" 200 36312
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
Internal Server Error: /students/dashboard/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'bulk_import' not found. 'bulk_import' is not a valid view function or pattern name.
"GET /students/dashboard/ HTTP/1.1" 500 186312
"GET /finance/account-statement/ HTTP/1.1" 200 30397
Internal Server Error: /finance/fees-payment/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'payment_history' not found. 'payment_history' is not a valid view function or pattern name.
"GET /finance/fees-payment/ HTTP/1.1" 500 187726
Internal Server Error: /hr/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'performance' not found. 'performance' is not a valid view function or pattern name.
"GET /hr/ HTTP/1.1" 500 186232
"GET /academics/teachers/ HTTP/1.1" 200 38161
"GET /static/images/default-avatar.png HTTP/1.1" 404 2002
"GET /static/images/default-avatar.png HTTP/1.1" 404 2002
Internal Server Error: /students/dashboard/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'bulk_import' not found. 'bulk_import' is not a valid view function or pattern name.
"GET /students/dashboard/ HTTP/1.1" 500 186451
Internal Server Error: /finance/fees-payment/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'payment_history' not found. 'payment_history' is not a valid view function or pattern name.
"GET /finance/fees-payment/ HTTP/1.1" 500 187864
"GET / HTTP/1.1" 302 0
"GET /accounts/dashboard/ HTTP/1.1" 200 22812
Internal Server Error: /hr/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'performance' not found. 'performance' is not a valid view function or pattern name.
"GET /hr/ HTTP/1.1" 500 186369
Watching for file changes with StatReloader
"GET /students/add/ HTTP/1.1" 200 32666
"GET /students/ HTTP/1.1" 200 36311
"GET /students/add/ HTTP/1.1" 200 32664
"GET /students/ HTTP/1.1" 200 36310
"GET /students/2/ HTTP/1.1" 200 38906
"GET /students/ HTTP/1.1" 200 36310
"GET /reports/attendance/ HTTP/1.1" 200 35183
D:\Python-projects\school-onlinepro\students\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\students\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\students\views.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\finance\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\finance\views.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\hr\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\hr\views.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /students/dashboard/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'bulk_import' not found. 'bulk_import' is not a valid view function or pattern name.
"GET /students/dashboard/ HTTP/1.1" 500 186190
Internal Server Error: /finance/fees-payment/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'payment_history' not found. 'payment_history' is not a valid view function or pattern name.
"GET /finance/fees-payment/ HTTP/1.1" 500 187604
Internal Server Error: /hr/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'performance' not found. 'performance' is not a valid view function or pattern name.
"GET /hr/ HTTP/1.1" 500 186110
Internal Server Error: /hr/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'performance' not found. 'performance' is not a valid view function or pattern name.
"GET /hr/ HTTP/1.1" 500 186247
Not Found: /students/bulk-import/
"GET /students/bulk-import/ HTTP/1.1" 404 26325
Not Found: /finance/payment-history/
"GET /finance/payment-history/ HTTP/1.1" 404 32137
Not Found: /hr/performance/
"GET /hr/performance/ HTTP/1.1" 404 30938
Watching for file changes with StatReloader
"GET /students/bulk-import/ HTTP/1.1" 200 42510
Internal Server Error: /students/dashboard/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'certificates' not found. 'certificates' is not a valid view function or pattern name.
"GET /students/dashboard/ HTTP/1.1" 500 185946
"GET /finance/fees-payment/ HTTP/1.1" 200 38507
"GET /hr/ HTTP/1.1" 200 39592
"GET /finance/payment-history/ HTTP/1.1" 200 42671
"GET /hr/performance/ HTTP/1.1" 200 43997
"GET /static/images/default-avatar.png HTTP/1.1" 404 2002
"GET /static/images/default-avatar.png HTTP/1.1" 404 2002
D:\Python-projects\school-onlinepro\students\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\students\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /students/dashboard/ HTTP/1.1" 200 32742
Internal Server Error: /students/reports/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: students/reports.html
"GET /students/reports/ HTTP/1.1" 500 100133
Internal Server Error: /students/id-cards/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: students/id_cards.html
"GET /students/id-cards/ HTTP/1.1" 500 100170
Internal Server Error: /students/certificates/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: students/certificates.html
"GET /students/certificates/ HTTP/1.1" 500 100333
"GET /students/bulk-import/ HTTP/1.1" 200 42509
"GET /students/ HTTP/1.1" 200 36311
Internal Server Error: /students/reports/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: students/reports.html
"GET /students/reports/ HTTP/1.1" 500 100133
Internal Server Error: /students/id-cards/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: students/id_cards.html
"GET /students/id-cards/ HTTP/1.1" 500 100171
Internal Server Error: /students/certificates/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: students/certificates.html
"GET /students/certificates/ HTTP/1.1" 500 100333
"GET /students/dashboard/ HTTP/1.1" 200 32741
"GET /hr/performance/ HTTP/1.1" 200 43997
"GET /static/images/default-avatar.png HTTP/1.1" 404 2002
"GET /static/images/default-avatar.png HTTP/1.1" 404 2002
Internal Server Error: /hr/reports/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: hr/reports.html
"GET /hr/reports/ HTTP/1.1" 500 99873
Internal Server Error: /hr/leaves/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: hr/vacation_requests.html
"GET /hr/leaves/ HTTP/1.1" 500 100157
Internal Server Error: /hr/payroll/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: hr/payroll.html
"GET /hr/payroll/ HTTP/1.1" 500 99869
Internal Server Error: /hr/attendance/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: hr/attendance.html
"GET /hr/attendance/ HTTP/1.1" 500 99988
"GET /students/reports/ HTTP/1.1" 200 37030
"GET /students/id-cards/ HTTP/1.1" 200 41756
"GET /static/images/default-avatar.png HTTP/1.1" 404 2002
"GET /static/images/default-avatar.png HTTP/1.1" 404 2002
"GET /students/certificates/ HTTP/1.1" 200 42949
D:\Python-projects\school-onlinepro\finance\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\finance\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /finance/accounts-tree/ HTTP/1.1" 200 46071
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/css/style.css HTTP/1.1" 304 0
D:\Python-projects\school-onlinepro\finance\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\finance\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /finance/journal-entries/ HTTP/1.1" 200 44375
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
"GET /admin/ HTTP/1.1" 200 29581
"GET /static/admin/css/base.css HTTP/1.1" 304 0
"GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
"GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive.css HTTP/1.1" 304 0
"GET /static/admin/js/theme.js HTTP/1.1" 304 0
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
"GET /accounts/dashboard/ HTTP/1.1" 200 22811
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /admin/ HTTP/1.1" 200 29579
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
"GET /admin/students/class/ HTTP/1.1" 200 35697
"GET /static/admin/js/core.js HTTP/1.1" 200 6208
"GET /static/admin/js/actions.js HTTP/1.1" 200 8076
"GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
"GET /static/admin/css/changelists.css HTTP/1.1" 200 6878
"GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
"GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 9777
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/img/search.svg HTTP/1.1" 200 458
"GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 285314
"GET /static/admin/js/filters.js HTTP/1.1" 200 978
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 325171
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
"GET /admin/students/class/1/change/ HTTP/1.1" 200 35836
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /static/admin/css/forms.css HTTP/1.1" 200 8525
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11991
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /admin/accounts/user/2/change/ HTTP/1.1" 200 56415
"GET /static/admin/js/SelectBox.js HTTP/1.1" 200 4530
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/js/calendar.js HTTP/1.1" 200 9141
"GET /static/admin/js/SelectFilter2.js HTTP/1.1" 200 15845
"GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
"GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
"GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
"GET /static/admin/img/selector-icons.svg HTTP/1.1" 200 3291
"GET /admin/students/class/1/change/ HTTP/1.1" 200 35835
"GET /admin/students/class/ HTTP/1.1" 200 35696
"GET /admin/ HTTP/1.1" 200 29579
"GET /admin/students/student/ HTTP/1.1" 200 34670
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
