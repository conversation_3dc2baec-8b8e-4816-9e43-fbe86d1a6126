from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView, ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Count, Avg, Sum
from django.http import HttpResponse, JsonResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import datetime, timedelta
from .models import (
    AcademicYear, Subject, Teacher, ClassSubject, Schedule, Exam, StudentGrade,
    StudentAttendance, Curriculum, CurriculumSubject
)
from .forms import (
    AcademicYearForm, SubjectForm, TeacherForm, ClassSubjectForm, ScheduleForm,
    ExamForm, StudentGradeForm, StudentAttendanceForm, CurriculumForm,
    CurriculumSubjectForm, BulkAttendanceForm, BulkGradeEntryForm
)
from students.models import Student, Grade, Class

def placeholder_view(request):
    return HttpResponse("Academics module - Coming soon!")

# Academic Dashboard
class AcademicDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Basic statistics
        context['total_subjects'] = Subject.objects.filter(is_active=True).count()
        context['total_teachers'] = Teacher.objects.filter(is_active=True).count()
        context['total_classes'] = ClassSubject.objects.filter(is_active=True).count()
        context['total_students'] = Student.objects.filter(is_active=True).count()

        # Current academic year
        current_year = AcademicYear.objects.filter(is_current=True).first()
        context['current_academic_year'] = current_year

        # Upcoming exams
        context['upcoming_exams'] = Exam.objects.filter(
            exam_date__gte=timezone.now().date(),
            is_published=True
        ).order_by('exam_date', 'start_time')[:5]

        # Recent grades
        context['recent_grades'] = StudentGrade.objects.select_related(
            'student', 'exam'
        ).order_by('-graded_at')[:10]

        # Today's schedule
        today = timezone.now().date()
        weekday = today.strftime('%A').lower()
        context['todays_schedule'] = Schedule.objects.filter(
            day_of_week=weekday,
            is_active=True
        ).select_related('class_subject', 'class_subject__subject', 'class_subject__teacher').order_by('start_time')[:10]

        # Attendance statistics for current month
        current_month = timezone.now().replace(day=1)
        next_month = (current_month + timedelta(days=32)).replace(day=1)

        total_attendance = StudentAttendance.objects.filter(
            date__gte=current_month,
            date__lt=next_month
        ).count()

        present_attendance = StudentAttendance.objects.filter(
            date__gte=current_month,
            date__lt=next_month,
            status__in=['present', 'late']
        ).count()

        context['attendance_rate'] = (present_attendance / total_attendance * 100) if total_attendance > 0 else 0

        # Subject performance (average grades)
        subject_performance = StudentGrade.objects.filter(
            exam__exam_date__gte=current_month
        ).values(
            'exam__class_subject__subject__name'
        ).annotate(
            avg_percentage=Avg('percentage')
        ).order_by('-avg_percentage')[:5]

        context['subject_performance'] = subject_performance

        # Chart data for performance
        subjects = [item['exam__class_subject__subject__name'] for item in subject_performance]
        averages = [float(item['avg_percentage']) if item['avg_percentage'] else 0 for item in subject_performance]

        context['performance_labels'] = subjects
        context['performance_data'] = averages

        return context

# Study Year Management
class StudyYearView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/study_year.html'

class AcademicYearListView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/academic_years.html'

class SemesterListView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/semesters.html'

# Subject Management
class SubjectListView(LoginRequiredMixin, ListView):
    model = Subject
    template_name = 'academics/subjects.html'
    context_object_name = 'subjects'
    paginate_by = 20

    def get_queryset(self):
        queryset = Subject.objects.all()
        search = self.request.GET.get('search')

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(name_ar__icontains=search) |
                Q(code__icontains=search)
            )

        return queryset.order_by('name')


class SubjectCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Subject
    form_class = SubjectForm
    template_name = 'academics/subject_form.html'
    permission_required = 'academics.add_subject'
    success_url = reverse_lazy('academics:subjects')

    def form_valid(self, form):
        messages.success(self.request, _('Subject created successfully!'))
        return super().form_valid(form)


class SubjectDetailView(LoginRequiredMixin, DetailView):
    model = Subject
    template_name = 'academics/subject_detail.html'
    context_object_name = 'subject'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        subject = self.get_object()

        # Get classes teaching this subject
        context['class_subjects'] = ClassSubject.objects.filter(
            subject=subject,
            is_active=True
        ).select_related('class_name', 'teacher')

        # Get recent exams
        context['recent_exams'] = Exam.objects.filter(
            class_subject__subject=subject
        ).order_by('-exam_date')[:5]

        return context


class SubjectUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Subject
    form_class = SubjectForm
    template_name = 'academics/subject_form.html'
    permission_required = 'academics.change_subject'
    success_url = reverse_lazy('academics:subjects')

    def form_valid(self, form):
        messages.success(self.request, _('Subject updated successfully!'))
        return super().form_valid(form)

class SubjectDeleteView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/subject_confirm_delete.html'

# Teacher Management
class TeacherListView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/teachers.html'

class TeacherCreateView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/teacher_form.html'

class TeacherDetailView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/teacher_detail.html'

class TeacherUpdateView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/teacher_form.html'

# Class Management
class ClassListView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/classes.html'

class ClassCreateView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/class_form.html'

class ClassDetailView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/class_detail.html'

class ClassUpdateView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/class_form.html'

# Schedule Management
class ScheduleListView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/schedules.html'

class ScheduleCreateView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/schedule_form.html'

class ScheduleDetailView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/schedule_detail.html'

class ScheduleUpdateView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/schedule_form.html'

class TimetableView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/timetable.html'

# Grades and Assessment
class GradeManagementView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/grades.html'

class GradeEntryView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/grade_entry.html'

class GradeReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/grade_reports.html'

class TranscriptsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/transcripts.html'

# Exams
class ExamListView(LoginRequiredMixin, ListView):
    model = Exam
    template_name = 'academics/exams.html'
    context_object_name = 'exams'
    paginate_by = 20

    def get_queryset(self):
        queryset = Exam.objects.select_related('class_subject', 'class_subject__subject', 'created_by').all()
        search = self.request.GET.get('search')
        exam_type = self.request.GET.get('exam_type')
        class_subject = self.request.GET.get('class_subject')

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(name_ar__icontains=search)
            )
        if exam_type:
            queryset = queryset.filter(exam_type=exam_type)
        if class_subject:
            queryset = queryset.filter(class_subject_id=class_subject)

        return queryset.order_by('-exam_date', '-start_time')


class ExamCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Exam
    form_class = ExamForm
    template_name = 'academics/exam_form.html'
    permission_required = 'academics.add_exam'
    success_url = reverse_lazy('academics:exams')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, _('Exam created successfully!'))
        return super().form_valid(form)


class ExamDetailView(LoginRequiredMixin, DetailView):
    model = Exam
    template_name = 'academics/exam_detail.html'
    context_object_name = 'exam'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        exam = self.get_object()

        # Get student grades for this exam
        context['student_grades'] = StudentGrade.objects.filter(
            exam=exam
        ).select_related('student').order_by('student__first_name', 'student__last_name')

        # Calculate statistics
        grades = context['student_grades']
        if grades:
            context['total_students'] = grades.count()
            context['average_marks'] = grades.aggregate(avg=Avg('marks_obtained'))['avg']
            context['highest_marks'] = grades.aggregate(max=Sum('marks_obtained'))['max']
            context['lowest_marks'] = grades.aggregate(min=Sum('marks_obtained'))['min']
            context['pass_count'] = grades.filter(marks_obtained__gte=exam.passing_marks).count()
            context['fail_count'] = grades.filter(marks_obtained__lt=exam.passing_marks).count()

        return context


class ExamUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Exam
    form_class = ExamForm
    template_name = 'academics/exam_form.html'
    permission_required = 'academics.change_exam'
    success_url = reverse_lazy('academics:exams')

    def form_valid(self, form):
        messages.success(self.request, _('Exam updated successfully!'))
        return super().form_valid(form)


class ExamScheduleView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/exam_schedule.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get upcoming exams
        context['upcoming_exams'] = Exam.objects.filter(
            exam_date__gte=timezone.now().date(),
            is_published=True
        ).select_related('class_subject', 'class_subject__subject').order_by('exam_date', 'start_time')

        return context


class ExamResultsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/exam_results.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get completed exams with results
        context['completed_exams'] = Exam.objects.filter(
            exam_date__lt=timezone.now().date(),
            is_published=True,
            student_grades__isnull=False
        ).distinct().select_related('class_subject', 'class_subject__subject').order_by('-exam_date')

        return context

# Attendance
class AttendanceView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/attendance.html'

class TakeAttendanceView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/take_attendance.html'

class AttendanceReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/attendance_reports.html'

class AttendanceSummaryView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/attendance_summary.html'

# Curriculum and Syllabus
class CurriculumView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/curriculum.html'

class SyllabusView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/syllabus.html'

class LessonPlansView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/lesson_plans.html'

# Academic Reports
class AcademicReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/reports.html'

class ProgressReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/progress_reports.html'

class PerformanceReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/performance_reports.html'

class ClassSummaryReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'academics/class_summary_reports.html'
